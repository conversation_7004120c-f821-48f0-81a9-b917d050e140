/**
 * Hook for real-time session monitoring
 * Automatically checks session validity and handles force logout
 */

import { useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

interface SessionMonitorOptions {
  role: 'admin' | 'student'
  intervalMs?: number // Default: 30 seconds
  onSessionInvalid?: () => void
  enabled?: boolean
}

export function useSessionMonitor({
  role,
  intervalMs = 30000, // Check every 30 seconds
  onSessionInvalid,
  enabled = true
}: SessionMonitorOptions) {
  const router = useRouter()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isCheckingRef = useRef(false)

  const checkSession = useCallback(async () => {
    // Prevent multiple concurrent checks
    if (isCheckingRef.current) return
    
    try {
      isCheckingRef.current = true
      
      const response = await fetch('/api/auth/check-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ role }),
        credentials: 'include'
      })

      if (response.status === 401) {
        // Session is invalid - user has been force logged out
        console.log('Session invalid - redirecting to login')
        
        // Clear any local storage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('user')
          localStorage.removeItem('session')
        }
        
        // Show notification
        toast.error('Your session has expired or been terminated. Please login again.')
        
        // Call custom handler if provided
        if (onSessionInvalid) {
          onSessionInvalid()
        }
        
        // Redirect to login
        const loginPath = role === 'admin' ? '/admin' : '/student'
        router.push(`${loginPath}?error=session_expired`)
        
        // Stop monitoring
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
          intervalRef.current = null
        }
      }
    } catch (error) {
      console.error('Session check failed:', error)
      // Don't redirect on network errors, just log
    } finally {
      isCheckingRef.current = false
    }
  }, [role, router, onSessionInvalid])

  useEffect(() => {
    if (!enabled) return

    // Start monitoring
    intervalRef.current = setInterval(checkSession, intervalMs)

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [checkSession, intervalMs, enabled])

  // Also check on window focus (user returns to tab)
  useEffect(() => {
    if (!enabled) return

    const handleFocus = () => {
      checkSession()
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [checkSession, enabled])

  // Manual session check function
  const manualCheck = useCallback(() => {
    return checkSession()
  }, [checkSession])

  return {
    checkSession: manualCheck
  }
}

/**
 * Hook for admin session monitoring
 */
export function useAdminSessionMonitor(options?: Omit<SessionMonitorOptions, 'role'>) {
  return useSessionMonitor({
    ...options,
    role: 'admin'
  })
}

/**
 * Hook for student session monitoring
 */
export function useStudentSessionMonitor(options?: Omit<SessionMonitorOptions, 'role'>) {
  return useSessionMonitor({
    ...options,
    role: 'student'
  })
}
