import {
  SessionD<PERSON>,
  CreateSessionD<PERSON>,
  Session<PERSON>ummary,
  SessionF<PERSON>er,
  SessionValidationResult,
} from '../../domain/entities/session'
import { SessionRepository } from '../../domain/repositories/session-repository'
import { RedisCache } from '../cache/redis'
import { broadcastSessionInvalidation } from '../../utils/session-broadcast'
import { v4 as uuidv4 } from 'uuid'

/**
 * Redis-based implementation of SessionRepository
 * Uses Redis for session storage with proper TTL management
 */
export class RedisSessionRepository implements SessionRepository {
  private readonly SESSION_PREFIX = 'session:'
  private readonly USER_SESSIONS_PREFIX = 'user_sessions:'
  private readonly DEVICE_SESSION_PREFIX = 'device_session:'
  private readonly SESSION_INDEX_PREFIX = 'session_index:'

  constructor(private cache: RedisCache) {}

  /**
   * Create a new session
   */
  async createSession(sessionData: CreateSessionDTO): Promise<SessionData> {
    const sessionId = uuidv4()
    const now = new Date()
    const expiresAt = new Date(now.getTime() + (sessionData.durationSeconds || 3600) * 1000) // Default 1 hour

    const session: SessionData = {
      sessionId,
      userId: sessionData.userId,
      role: sessionData.role,
      deviceId: sessionData.deviceId,
      ipAddress: sessionData.ipAddress,
      userAgent: sessionData.userAgent,
      createdAt: now,
      lastAccessedAt: now,
      expiresAt,
      isActive: true,
      metadata: sessionData.metadata,
    }

    const ttlSeconds = Math.floor((expiresAt.getTime() - now.getTime()) / 1000)

    // Store session data
    await this.cache.set(`${this.SESSION_PREFIX}${sessionId}`, JSON.stringify(session), ttlSeconds)

    // Index session by user ID
    await this.addToUserSessionsIndex(sessionData.userId, sessionId, ttlSeconds)

    // Index session by device
    await this.cache.set(
      `${this.DEVICE_SESSION_PREFIX}${sessionData.userId}:${sessionData.deviceId}`,
      sessionId,
      ttlSeconds
    )

    // Add to global session index for listing
    await this.addToGlobalSessionIndex(sessionId, session, ttlSeconds)

    // Update counters for performance optimization
    await this.incrementSessionCounters(session.role, ttlSeconds)

    return session
  }

  /**
   * Get a session by session ID
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    const sessionJson = await this.cache.get(`${this.SESSION_PREFIX}${sessionId}`)

    if (!sessionJson) {
      return null
    }

    try {
      const session = JSON.parse(sessionJson) as SessionData

      // Convert date strings back to Date objects
      session.createdAt = new Date(session.createdAt)
      session.lastAccessedAt = new Date(session.lastAccessedAt)
      session.expiresAt = new Date(session.expiresAt)

      // Check if session is expired
      if (session.expiresAt < new Date()) {
        await this.invalidateSession(sessionId)
        return null
      }

      return session
    } catch (error) {
      console.error('Error parsing session data:', error)
      return null
    }
  }

  /**
   * Get a session by user ID and device ID
   */
  async getSessionByUserAndDevice(userId: number, deviceId: string): Promise<SessionData | null> {
    const sessionId = await this.cache.get(`${this.DEVICE_SESSION_PREFIX}${userId}:${deviceId}`)

    if (!sessionId) {
      return null
    }

    return await this.getSession(sessionId)
  }

  /**
   * Get all active sessions for a user
   */
  async getActiveSessionsForUser(userId: number): Promise<SessionData[]> {
    const sessionIdsJson = await this.cache.get(`${this.USER_SESSIONS_PREFIX}${userId}`)

    if (!sessionIdsJson) {
      return []
    }

    try {
      const sessionIds = JSON.parse(sessionIdsJson) as string[]
      const sessions: SessionData[] = []

      for (const sessionId of sessionIds) {
        const session = await this.getSession(sessionId)
        if (session && session.isActive) {
          sessions.push(session)
        }
      }

      return sessions
    } catch (error) {
      console.error('Error parsing user sessions:', error)
      return []
    }
  }

  /**
   * Update session last accessed time
   */
  async updateLastAccessed(sessionId: string): Promise<SessionData | null> {
    const session = await this.getSession(sessionId)

    if (!session) {
      return null
    }

    session.lastAccessedAt = new Date()

    const ttlSeconds = Math.floor((session.expiresAt.getTime() - Date.now()) / 1000)

    if (ttlSeconds > 0) {
      await this.cache.set(
        `${this.SESSION_PREFIX}${sessionId}`,
        JSON.stringify(session),
        ttlSeconds
      )

      // Update global index
      await this.addToGlobalSessionIndex(sessionId, session, ttlSeconds)
    }

    return session
  }

  /**
   * Invalidate a specific session
   */
  async invalidateSession(sessionId: string): Promise<boolean> {
    const session = await this.getSession(sessionId)

    if (!session) {
      return false
    }

    // Remove session data
    await this.cache.del(`${this.SESSION_PREFIX}${sessionId}`)

    // Remove from user sessions index
    await this.removeFromUserSessionsIndex(session.userId, sessionId)

    // Remove device session mapping
    await this.cache.del(`${this.DEVICE_SESSION_PREFIX}${session.userId}:${session.deviceId}`)

    // Remove from global index
    await this.removeFromGlobalSessionIndex(sessionId)

    // Update counters for performance optimization
    await this.decrementSessionCounters(session.role)

    // Broadcast session invalidation for real-time logout
    broadcastSessionInvalidation(session.userId, sessionId, 'admin_action')

    return true
  }

  /**
   * Invalidate all sessions for a user
   */
  async invalidateAllUserSessions(userId: number): Promise<number> {
    const sessions = await this.getActiveSessionsForUser(userId)
    let invalidatedCount = 0

    for (const session of sessions) {
      const success = await this.invalidateSession(session.sessionId)
      if (success) {
        invalidatedCount++
      }
    }

    return invalidatedCount
  }

  /**
   * Invalidate all sessions for a user except the current one
   */
  async invalidateOtherUserSessions(userId: number, currentSessionId: string): Promise<number> {
    const sessions = await this.getActiveSessionsForUser(userId)
    let invalidatedCount = 0

    for (const session of sessions) {
      if (session.sessionId !== currentSessionId) {
        const success = await this.invalidateSession(session.sessionId)
        if (success) {
          invalidatedCount++
        }
      }
    }

    return invalidatedCount
  }

  /**
   * Validate a session and optionally refresh it
   */
  async validateSession(
    sessionId: string,
    refreshIfValid: boolean = true
  ): Promise<SessionValidationResult> {
    const session = await this.getSession(sessionId)

    if (!session) {
      return {
        isValid: false,
        error: 'Session not found',
      }
    }

    if (!session.isActive) {
      return {
        isValid: false,
        error: 'Session is inactive',
      }
    }

    if (session.expiresAt < new Date()) {
      await this.invalidateSession(sessionId)
      return {
        isValid: false,
        error: 'Session has expired',
      }
    }

    let refreshed = false
    if (refreshIfValid) {
      await this.updateLastAccessed(sessionId)
      refreshed = true
    }

    return {
      isValid: true,
      session,
      refreshed,
    }
  }

  /**
   * List sessions with optional filtering - OPTIMIZED for high volume (2000-3000+ sessions)
   */
  async listSessions(filter?: SessionFilter): Promise<SessionSummary[]> {
    try {
      // For performance with large session counts, we use different strategies based on filter
      if (filter?.userId) {
        // If filtering by userId, use user-specific index (much faster)
        return await this.listSessionsByUser(filter.userId, filter)
      }

      if (filter?.role) {
        // If filtering by role, use role-specific index
        return await this.listSessionsByRole(filter.role, filter)
      }

      // For general listing, use paginated approach with Redis SCAN
      return await this.listSessionsPaginated(filter)
    } catch (error) {
      console.error('Error listing sessions:', error)
      return []
    }
  }

  /**
   * Get session count by filter criteria - OPTIMIZED
   */
  async getSessionCount(filter?: SessionFilter): Promise<number> {
    try {
      if (filter?.userId) {
        // Count user sessions directly from user index
        const userSessions = await this.getActiveSessionsForUser(filter.userId)
        return userSessions.filter(session => this.matchesFilter(session, filter)).length
      }

      if (filter?.role) {
        // Count by role using role-specific counter
        const roleCountKey = `${this.SESSION_INDEX_PREFIX}count:role:${filter.role}`
        const countStr = await this.cache.get(roleCountKey)
        return countStr ? parseInt(countStr, 10) : 0
      }

      // For general count, use cached total count
      const totalCountKey = `${this.SESSION_INDEX_PREFIX}count:total`
      const countStr = await this.cache.get(totalCountKey)
      return countStr ? parseInt(countStr, 10) : 0
    } catch (error) {
      console.error('Error getting session count:', error)
      return 0
    }
  }

  /**
   * List sessions by user ID (optimized for user-specific queries)
   */
  private async listSessionsByUser(
    userId: number,
    filter?: SessionFilter
  ): Promise<SessionSummary[]> {
    const sessions = await this.getActiveSessionsForUser(userId)
    const filteredSessions = sessions
      .filter(session => this.matchesFilter(session, filter))
      .map(session => this.toSessionSummary(session))

    // Apply pagination
    const start = filter?.offset || 0
    const end = filter?.limit ? start + filter.limit : undefined
    return filteredSessions.slice(start, end)
  }

  /**
   * List sessions by role (optimized for role-specific queries)
   */
  private async listSessionsByRole(
    role: string,
    filter?: SessionFilter
  ): Promise<SessionSummary[]> {
    const roleIndexKey = `${this.SESSION_INDEX_PREFIX}role:${role}`
    const sessionIdsJson = await this.cache.get(roleIndexKey)

    if (!sessionIdsJson) {
      return []
    }

    try {
      const sessionIds = JSON.parse(sessionIdsJson) as string[]
      const sessions: SessionSummary[] = []

      // Apply pagination at the ID level to reduce Redis calls
      const start = filter?.offset || 0
      const limit = filter?.limit || 50
      const paginatedIds = sessionIds.slice(start, start + limit)

      // Only fetch the sessions we need for this page
      for (const sessionId of paginatedIds) {
        const session = await this.getSession(sessionId)
        if (session && this.matchesFilter(session, filter)) {
          sessions.push(this.toSessionSummary(session))
        }
      }

      return sessions
    } catch (error) {
      console.error('Error listing sessions by role:', error)
      return []
    }
  }

  /**
   * List sessions with pagination (optimized for general queries)
   */
  private async listSessionsPaginated(filter?: SessionFilter): Promise<SessionSummary[]> {
    const allSessionsJson = await this.cache.get(`${this.SESSION_INDEX_PREFIX}all`)

    if (!allSessionsJson) {
      return []
    }

    try {
      const allSessionIds = JSON.parse(allSessionsJson) as string[]

      // Apply pagination at the ID level to reduce Redis calls
      const start = filter?.offset || 0
      const limit = filter?.limit || 50
      const paginatedIds = allSessionIds.slice(start, start + limit * 2) // Get a bit more to account for filtering

      const sessions: SessionSummary[] = []
      let collected = 0
      const targetLimit = limit

      // Only fetch sessions until we have enough for this page
      for (const sessionId of paginatedIds) {
        if (collected >= targetLimit) break

        const session = await this.getSession(sessionId)
        if (session && this.matchesFilter(session, filter)) {
          sessions.push(this.toSessionSummary(session))
          collected++
        }
      }

      return sessions
    } catch (error) {
      console.error('Error listing sessions paginated:', error)
      return []
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    // Redis TTL handles most cleanup automatically
    // This method can be used for additional cleanup if needed
    return 0
  }

  /**
   * Get session statistics - OPTIMIZED for high volume
   */
  async getSessionStats(): Promise<{
    totalSessions: number
    activeSessions: number
    expiredSessions: number
    sessionsByRole: Record<string, number>
  }> {
    try {
      // Use cached counters for better performance with large session counts
      const [totalCountStr, activeCountStr, studentCountStr, adminCountStr, superAdminCountStr] =
        await Promise.all([
          this.cache.get(`${this.SESSION_INDEX_PREFIX}count:total`),
          this.cache.get(`${this.SESSION_INDEX_PREFIX}count:active`),
          this.cache.get(`${this.SESSION_INDEX_PREFIX}count:role:student`),
          this.cache.get(`${this.SESSION_INDEX_PREFIX}count:role:admin`),
          this.cache.get(`${this.SESSION_INDEX_PREFIX}count:role:super_admin`),
        ])

      const totalSessions = totalCountStr ? parseInt(totalCountStr, 10) : 0
      const activeSessions = activeCountStr ? parseInt(activeCountStr, 10) : 0
      const studentSessions = studentCountStr ? parseInt(studentCountStr, 10) : 0
      const adminSessions = adminCountStr ? parseInt(adminCountStr, 10) : 0
      const superAdminSessions = superAdminCountStr ? parseInt(superAdminCountStr, 10) : 0

      return {
        totalSessions,
        activeSessions,
        expiredSessions: totalSessions - activeSessions,
        sessionsByRole: {
          student: studentSessions,
          admin: adminSessions,
          super_admin: superAdminSessions,
        },
      }
    } catch (error) {
      console.error('Error getting session stats:', error)
      // Fallback to basic stats if cached counters fail
      return {
        totalSessions: 0,
        activeSessions: 0,
        expiredSessions: 0,
        sessionsByRole: {},
      }
    }
  }

  /**
   * Force logout a user from all devices
   */
  async forceLogoutUser(userId: number): Promise<number> {
    // Broadcast force logout event before invalidating sessions
    broadcastSessionInvalidation(userId, undefined, 'force_logout')

    return await this.invalidateAllUserSessions(userId)
  }

  /**
   * Check if user has active sessions
   */
  async hasActiveSessions(userId: number): Promise<boolean> {
    const sessions = await this.getActiveSessionsForUser(userId)
    return sessions.length > 0
  }

  /**
   * Get user's current device sessions
   */
  async getUserDeviceSessions(userId: number): Promise<
    Array<{
      deviceId: string
      sessionId: string
      lastAccessedAt: Date
      deviceType?: string
      browser?: string
    }>
  > {
    const sessions = await this.getActiveSessionsForUser(userId)

    return sessions.map(session => ({
      deviceId: session.deviceId,
      sessionId: session.sessionId,
      lastAccessedAt: session.lastAccessedAt,
      deviceType: session.metadata?.deviceType,
      browser: session.metadata?.browser,
    }))
  }

  /**
   * Add session to user sessions index
   */
  private async addToUserSessionsIndex(
    userId: number,
    sessionId: string,
    ttlSeconds: number
  ): Promise<void> {
    const key = `${this.USER_SESSIONS_PREFIX}${userId}`
    const existingSessionsJson = await this.cache.get(key)

    let sessionIds: string[] = []
    if (existingSessionsJson) {
      try {
        sessionIds = JSON.parse(existingSessionsJson)
      } catch (error) {
        console.error('Error parsing user sessions index:', error)
      }
    }

    if (!sessionIds.includes(sessionId)) {
      sessionIds.push(sessionId)
    }

    await this.cache.set(key, JSON.stringify(sessionIds), ttlSeconds)
  }

  /**
   * Remove session from user sessions index
   */
  private async removeFromUserSessionsIndex(userId: number, sessionId: string): Promise<void> {
    const key = `${this.USER_SESSIONS_PREFIX}${userId}`
    const existingSessionsJson = await this.cache.get(key)

    if (!existingSessionsJson) {
      return
    }

    try {
      const sessionIds = JSON.parse(existingSessionsJson) as string[]
      const filteredIds = sessionIds.filter(id => id !== sessionId)

      if (filteredIds.length > 0) {
        await this.cache.set(key, JSON.stringify(filteredIds), 3600) // Keep for 1 hour
      } else {
        await this.cache.del(key)
      }
    } catch (error) {
      console.error('Error updating user sessions index:', error)
    }
  }

  /**
   * Add session to global session index
   */
  private async addToGlobalSessionIndex(
    sessionId: string,
    session: SessionData,
    ttlSeconds: number
  ): Promise<void> {
    // Add to global index
    const globalKey = `${this.SESSION_INDEX_PREFIX}all`
    const existingSessionsJson = await this.cache.get(globalKey)

    let sessionIds: string[] = []
    if (existingSessionsJson) {
      try {
        sessionIds = JSON.parse(existingSessionsJson)
      } catch (error) {
        console.error('Error parsing global sessions index:', error)
      }
    }

    if (!sessionIds.includes(sessionId)) {
      sessionIds.push(sessionId)
    }

    await this.cache.set(globalKey, JSON.stringify(sessionIds), Math.max(ttlSeconds, 3600))

    // Also add to role-specific index for optimized role filtering
    await this.addSessionToRoleIndex(sessionId, session.role, ttlSeconds)
  }

  /**
   * Remove session from global session index
   */
  private async removeFromGlobalSessionIndex(sessionId: string): Promise<void> {
    // Remove from global index
    const globalKey = `${this.SESSION_INDEX_PREFIX}all`
    const existingSessionsJson = await this.cache.get(globalKey)

    if (!existingSessionsJson) {
      return
    }

    try {
      const sessionIds = JSON.parse(existingSessionsJson) as string[]
      const filteredIds = sessionIds.filter(id => id !== sessionId)

      if (filteredIds.length > 0) {
        await this.cache.set(globalKey, JSON.stringify(filteredIds), 3600)
      } else {
        await this.cache.del(globalKey)
      }

      // Also remove from all role indexes
      await this.removeSessionFromAllRoleIndexes(sessionId)
    } catch (error) {
      console.error('Error updating global sessions index:', error)
    }
  }

  /**
   * Add session to role-specific index
   */
  private async addSessionToRoleIndex(
    sessionId: string,
    role: string,
    ttlSeconds: number
  ): Promise<void> {
    try {
      const roleIndexKey = `${this.SESSION_INDEX_PREFIX}role:${role}`
      const existingSessionsJson = await this.cache.get(roleIndexKey)

      let sessionIds: string[] = []
      if (existingSessionsJson) {
        try {
          sessionIds = JSON.parse(existingSessionsJson)
        } catch (error) {
          console.error('Error parsing role sessions index:', error)
        }
      }

      if (!sessionIds.includes(sessionId)) {
        sessionIds.push(sessionId)
      }

      await this.cache.set(roleIndexKey, JSON.stringify(sessionIds), Math.max(ttlSeconds, 3600))
    } catch (error) {
      console.error(`Error adding session to role index ${role}:`, error)
    }
  }

  /**
   * Remove session from all role indexes
   */
  private async removeSessionFromAllRoleIndexes(sessionId: string): Promise<void> {
    const roles = ['student', 'admin', 'super_admin']

    for (const role of roles) {
      try {
        const roleIndexKey = `${this.SESSION_INDEX_PREFIX}role:${role}`
        const existingSessionsJson = await this.cache.get(roleIndexKey)

        if (!existingSessionsJson) {
          continue
        }

        const sessionIds = JSON.parse(existingSessionsJson) as string[]
        const filteredIds = sessionIds.filter(id => id !== sessionId)

        if (filteredIds.length > 0) {
          await this.cache.set(roleIndexKey, JSON.stringify(filteredIds), 3600)
        } else {
          await this.cache.del(roleIndexKey)
        }
      } catch (error) {
        console.error(`Error removing session from role index ${role}:`, error)
      }
    }
  }

  /**
   * Check if session matches filter criteria
   */
  private matchesFilter(session: SessionData, filter?: SessionFilter): boolean {
    if (!filter) {
      return true
    }

    if (filter.userId && session.userId !== filter.userId) {
      return false
    }

    if (filter.role && session.role !== filter.role) {
      return false
    }

    if (filter.isActive !== undefined && session.isActive !== filter.isActive) {
      return false
    }

    if (filter.deviceId && session.deviceId !== filter.deviceId) {
      return false
    }

    if (filter.createdAfter && session.createdAt < filter.createdAfter) {
      return false
    }

    if (filter.createdBefore && session.createdAt > filter.createdBefore) {
      return false
    }

    return true
  }

  /**
   * Convert SessionData to SessionSummary
   */
  private toSessionSummary(session: SessionData): SessionSummary {
    return {
      sessionId: session.sessionId,
      userId: session.userId,
      userName: '', // Will be filled by use case layer
      role: session.role,
      deviceId: session.deviceId,
      ipAddress: session.ipAddress,
      deviceType: session.metadata?.deviceType || 'Unknown',
      browser: session.metadata?.browser || 'Unknown',
      createdAt: session.createdAt,
      lastAccessedAt: session.lastAccessedAt,
      expiresAt: session.expiresAt,
      isActive: session.isActive,
    }
  }

  /**
   * Increment session counters for performance optimization
   */
  private async incrementSessionCounters(role: string, ttlSeconds: number): Promise<void> {
    try {
      const counterTTL = Math.max(ttlSeconds, 3600) // At least 1 hour

      // Increment total sessions counter
      await this.incrementCounter(`${this.SESSION_INDEX_PREFIX}count:total`, counterTTL)

      // Increment active sessions counter
      await this.incrementCounter(`${this.SESSION_INDEX_PREFIX}count:active`, counterTTL)

      // Increment role-specific counter
      await this.incrementCounter(`${this.SESSION_INDEX_PREFIX}count:role:${role}`, counterTTL)
    } catch (error) {
      console.error('Error incrementing session counters:', error)
    }
  }

  /**
   * Decrement session counters for performance optimization
   */
  private async decrementSessionCounters(role: string): Promise<void> {
    try {
      // Decrement total sessions counter
      await this.decrementCounter(`${this.SESSION_INDEX_PREFIX}count:total`)

      // Decrement active sessions counter
      await this.decrementCounter(`${this.SESSION_INDEX_PREFIX}count:active`)

      // Decrement role-specific counter
      await this.decrementCounter(`${this.SESSION_INDEX_PREFIX}count:role:${role}`)
    } catch (error) {
      console.error('Error decrementing session counters:', error)
    }
  }

  /**
   * Increment a counter in Redis
   */
  private async incrementCounter(key: string, ttlSeconds: number): Promise<void> {
    try {
      const currentStr = await this.cache.get(key)
      const current = currentStr ? parseInt(currentStr, 10) : 0
      const newValue = current + 1
      await this.cache.set(key, newValue.toString(), ttlSeconds)
    } catch (error) {
      console.error(`Error incrementing counter ${key}:`, error)
    }
  }

  /**
   * Decrement a counter in Redis
   */
  private async decrementCounter(key: string): Promise<void> {
    try {
      const currentStr = await this.cache.get(key)
      const current = currentStr ? parseInt(currentStr, 10) : 0
      const newValue = Math.max(0, current - 1)

      if (newValue > 0) {
        await this.cache.set(key, newValue.toString(), 3600) // Keep for 1 hour
      } else {
        await this.cache.del(key)
      }
    } catch (error) {
      console.error(`Error decrementing counter ${key}:`, error)
    }
  }
}
