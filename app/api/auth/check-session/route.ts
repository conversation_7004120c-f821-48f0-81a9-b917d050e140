import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithSession, clearAuthCookies } from '@/lib/middleware/enhanced-auth'

/**
 * GET /api/auth/check-session
 * Check if current session is valid and clear cookies if not
 */
export async function GET(request: NextRequest) {
  try {
    // Try to authenticate with session
    const authResult = await authenticateWithSession(request)

    return NextResponse.json({
      valid: true,
      user: {
        id: authResult.id,
        role: authResult.role,
        sessionId: authResult.sessionId,
      },
    })
  } catch (error) {
    console.log('Session check failed:', error)

    // Session is invalid, create response to clear cookies
    const response = NextResponse.json(
      {
        valid: false,
        error: 'Session invalid or expired',
      },
      { status: 401 }
    )

    // Try to determine role from cookies to clear the right ones
    const adminToken = request.cookies.get('admin_auth_token')?.value
    const studentToken = request.cookies.get('student_auth_token')?.value

    if (adminToken) {
      clearAuthCookies(response, 'admin')
    }
    if (studentToken) {
      clearAuthCookies(response, 'student')
    }

    return response
  }
}

/**
 * POST /api/auth/check-session
 * Check session for specific role
 */
export async function POST(request: NextRequest) {
  let role: string | undefined

  try {
    const body = await request.json()
    role = body.role

    if (!role || !['admin', 'student'].includes(role)) {
      return NextResponse.json({ error: 'Invalid role specified' }, { status: 400 })
    }

    // Try to authenticate with specific role
    const authResult = await authenticateWithSession(request, role as 'admin' | 'student')

    return NextResponse.json({
      valid: true,
      user: {
        id: authResult.id,
        role: authResult.role,
        sessionId: authResult.sessionId,
      },
    })
  } catch (error) {
    console.log('Session check failed for role:', role, error)

    // Session is invalid, create response to clear cookies
    const response = NextResponse.json(
      {
        valid: false,
        error: 'Session invalid or expired',
      },
      { status: 401 }
    )

    // Clear cookies for the specified role
    if (role && ['admin', 'student'].includes(role)) {
      clearAuthCookies(response, role as 'admin' | 'student')
    }

    return response
  }
}
