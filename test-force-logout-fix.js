/**
 * Test force logout fix
 */

async function testForceLogoutFix() {
  try {
    console.log('Testing force logout fix...')
    
    // 1. Login admin
    console.log('\n1. Login admin...')
    const adminLoginResponse = await fetch('http://localhost:3001/api/auth/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    })

    const adminLoginData = await adminLoginResponse.json()
    const adminCookies = adminLoginResponse.headers.get('set-cookie')
    console.log('✅ Admin login successful!')
    console.log('Admin session ID:', adminLoginData.session?.sessionId)
    
    // 2. Login student
    console.log('\n2. Login student...')
    const studentLoginResponse = await fetch('http://localhost:3001/api/auth/student/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'muxaakl7541',
        password: 'changeme123'
      })
    })

    const studentLoginData = await studentLoginResponse.json()
    const studentCookies = studentLoginResponse.headers.get('set-cookie')
    console.log('✅ Student login successful!')
    
    // 3. Get all sessions
    console.log('\n3. Get all sessions...')
    const sessionListResponse = await fetch('http://localhost:3001/api/admin/sessions', {
      headers: {
        'Cookie': adminCookies || ''
      }
    })

    const sessionListData = await sessionListResponse.json()
    console.log(`Found ${sessionListData.data.sessions.length} sessions:`)
    
    sessionListData.data.sessions.forEach((session, index) => {
      console.log(`${index + 1}. ${session.userName} (${session.role}) - ${session.sessionId}`)
    })
    
    // 4. Try to force logout student session
    const studentSession = sessionListData.data.sessions.find(s => s.role === 'student')
    if (studentSession) {
      console.log(`\n4. Force logout student session: ${studentSession.sessionId}`)
      const forceLogoutResponse = await fetch('http://localhost:3001/api/admin/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': adminCookies || ''
        },
        body: JSON.stringify({
          sessionId: studentSession.sessionId
        })
      })

      const forceLogoutData = await forceLogoutResponse.json()
      
      if (forceLogoutResponse.ok) {
        console.log('✅ Force logout student successful!')
        console.log('Result:', forceLogoutData)
      } else {
        console.log('❌ Force logout student failed!')
        console.log('Error:', forceLogoutData)
      }
    }
    
    // 5. Try to force logout admin's own session (should fail)
    const adminSession = sessionListData.data.sessions.find(s => s.role === 'super_admin' && s.userId === adminLoginData.admin.id)
    if (adminSession) {
      console.log(`\n5. Try to force logout own session: ${adminSession.sessionId}`)
      const forceLogoutOwnResponse = await fetch('http://localhost:3001/api/admin/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': adminCookies || ''
        },
        body: JSON.stringify({
          sessionId: adminSession.sessionId
        })
      })

      const forceLogoutOwnData = await forceLogoutOwnResponse.json()
      
      if (forceLogoutOwnResponse.ok) {
        console.log('❌ Force logout own session succeeded (should have failed)!')
        console.log('Result:', forceLogoutOwnData)
      } else {
        console.log('✅ Force logout own session failed as expected!')
        console.log('Error:', forceLogoutOwnData)
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run test
testForceLogoutFix()
