'use client'

import type { ReactNode } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ThemeToggle } from '@/components/theme-toggle'
import { StudentBottomNav } from '@/components/student-bottom-nav'
import { useStudentSessionMonitor } from '@/hooks/use-session-monitor'

interface StudentLayoutProps {
  children: ReactNode
}

export function StudentLayout({ children }: StudentLayoutProps) {
  const pathname = usePathname()

  // Enable session monitoring for students
  useStudentSessionMonitor({
    enabled: true,
    intervalMs: 30000, // Check every 30 seconds
  })

  // Tidak perlu state untuk menu mobile lagi

  const navItems = [
    { label: 'Home', href: '/student/home' },
    // { label: "Riwayat", href: "/student/history" },
    { label: 'Profil', href: '/student/profile' },
  ]

  // Determine active tab for mobile bottom nav
  const getActiveTab = () => {
    if (pathname === '/student/home') return 'home'
    if (pathname === '/student/history') return 'history'
    if (pathname === '/student/profile') return 'profile'
    return 'home'
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* Header with navigation - tampil di semua ukuran layar */}
      <header className="sticky top-0 z-30 hidden border-b border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800 md:block">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center">
              {/* Mobile menu toggle */}
              <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">ShalatYuk</h1>
            </div>

            {/* Desktop navigation */}
            <nav className="hidden items-center space-x-6 md:flex">
              {navItems.map(item => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`px-3 py-2 transition-colors ${
                    pathname === item.href
                      ? 'font-medium text-indigo-600 dark:text-indigo-400'
                      : 'text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200'
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </nav>

            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto px-4 pb-16 md:pb-6">{children}</main>

      {/* Bottom navigation - hanya tampil di mobile */}
      <div className="md:hidden">
        <StudentBottomNav activeTab={getActiveTab() as any} />
      </div>
    </div>
  )
}
